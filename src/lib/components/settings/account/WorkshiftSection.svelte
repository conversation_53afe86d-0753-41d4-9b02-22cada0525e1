<script>
	import { t } from '$lib/stores/i18n';
	import { onMount } from 'svelte';
	import { Alert, Toggle, Checkbox, Button, Select, Label } from 'flowbite-svelte';
	import { invalidateAll } from '$app/navigation';

	export let dominantColor;
	export let user_schedule;
	export let business_hours;

	// our reactive state
	let days = [];
	let sameAsBusinessHours = false;

	// Time validation state
	let timeValidationErrors = {};
	let hasValidationErrors = false;

	// build your time dropdown
	const timeOptions = Array.from({ length: 48 }, (_, i) => {
		const hour = Math.floor(i / 2)
			.toString()
			.padStart(2, '0');
		const minutes = i % 2 === 0 ? '00' : '30';
		return `${hour}:${minutes}`;
	});

	// Function to convert time string (HH:MM) to minutes since midnight for comparison
	function timeToMinutes(timeStr) {
		if (!timeStr) return 0;
		const [hours, minutes] = timeStr.split(':').map(Number);
		return hours * 60 + minutes;
	}

	// Function to validate time range for a specific day
	function validateDayTimes(dayIndex) {
		const day = days[dayIndex];
		if (!day || !day.active || !day.times || !day.times[0]) {
			// Clear any existing errors for this day
			delete timeValidationErrors[dayIndex];
			timeValidationErrors = { ...timeValidationErrors };
			return true;
		}

		const startTime = day.times[0].start;
		const endTime = day.times[0].end;

		if (!startTime || !endTime) {
			delete timeValidationErrors[dayIndex];
			timeValidationErrors = { ...timeValidationErrors };
			return true;
		}

		const startMinutes = timeToMinutes(startTime);
		const endMinutes = timeToMinutes(endTime);

		if (startMinutes >= endMinutes) {
			timeValidationErrors[dayIndex] =
				t('time_validation_start_before_end') || 'Start time must be before end time';
			timeValidationErrors = { ...timeValidationErrors };
			return false;
		} else {
			delete timeValidationErrors[dayIndex];
			timeValidationErrors = { ...timeValidationErrors };
			return true;
		}
	}

	// Reactive statement to check if there are any validation errors
	$: hasValidationErrors = Object.keys(timeValidationErrors).length > 0;

	// Function to validate all days
	function validateAllDays() {
		let allValid = true;
		for (let i = 0; i < days.length; i++) {
			if (!validateDayTimes(i)) {
				allValid = false;
			}
		}
		return allValid;
	}

	// Function to map day names to translation keys (adapted from BusinessHour.svelte)
	function getDayTranslationKey(dayName) {
		if (!dayName) return 'day_monday'; // fallback

		const normalizedDay = dayName.toLowerCase().trim();

		// Handle various day name formats
		const dayMapping = {
			monday: 'day_monday',
			mon: 'day_monday',
			tuesday: 'day_tuesday',
			tue: 'day_tuesday',
			wednesday: 'day_wednesday',
			wed: 'day_wednesday',
			thursday: 'day_thursday',
			thu: 'day_thursday',
			friday: 'day_friday',
			fri: 'day_friday',
			saturday: 'day_saturday',
			sat: 'day_saturday',
			sunday: 'day_sunday',
			sun: 'day_sunday',
			// Handle Thai day names in case server sends them
			วันจันทร์: 'day_monday',
			วันอังคาร: 'day_tuesday',
			วันพุธ: 'day_wednesday',
			วันพฤหัสบดี: 'day_thursday',
			วันศุกร์: 'day_friday',
			วันเสาร์: 'day_saturday',
			วันอาทิตย์: 'day_sunday'
		};

		return dayMapping[normalizedDay] || 'day_monday'; // fallback to Monday
	}

	// Fixed onMount function to load correct data based on toggle state
	onMount(() => {
		sameAsBusinessHours = user_schedule?.sameAsBusinessHours ?? false;

		if (sameAsBusinessHours && business_hours?.workShift) {
			// Load business hours when toggle is on
			days = JSON.parse(JSON.stringify(business_hours.workShift));
		} else if (user_schedule?.workShift) {
			// Load user's custom schedule when toggle is off
			days = JSON.parse(JSON.stringify(user_schedule.workShift));
		} else if (business_hours?.workShift) {
			// Fallback to business hours if no user schedule exists
			days = JSON.parse(JSON.stringify(business_hours.workShift));
		}

		// Validate all days after loading
		setTimeout(() => validateAllDays(), 0);
	});

	function toggleDay(index) {
		days[index].active = !days[index].active;
		days = [...days]; // force reactivity
		// Validate the day after toggling
		setTimeout(() => validateDayTimes(index), 0);
	}

	function toggleSameAsBusinessHours() {
		if (sameAsBusinessHours && business_hours?.workShift) {
			// swap in the biz‐hours copy
			days = JSON.parse(JSON.stringify(business_hours.workShift));
		} else if (user_schedule?.workShift) {
			// Load user's custom schedule when toggle is turned off
			days = JSON.parse(JSON.stringify(user_schedule.workShift));
		}
		// Validate all days after switching
		setTimeout(() => validateAllDays(), 0);
	}

	// Handle time change with validation
	function handleTimeChange(dayIndex, timeType) {
		days = [...days]; // force reactivity
		setTimeout(() => validateDayTimes(dayIndex), 0);
	}

	// Handle form submission with validation
	function handleSubmit(event) {
		if (!validateAllDays()) {
			event.preventDefault();
			return false;
		}
		console.log('Yayy');
		invalidateAll();
		return true;
	}
</script>

<form
	method="POST"
	action="?/update_user_work_schedule"
	class="space-y-4 rounded-lg bg-white p-6 shadow-md"
	on:submit={handleSubmit}
>
	<div class="flex w-full items-center justify-between">
		<div>
			<h2 class="text-xl font-medium text-gray-700">
				{t('work_shift_title')}
			</h2>
		</div>

		<!-- submit as a real form -->
		<Button
			type="submit"
			disabled={hasValidationErrors}
			class="rounded-lg font-medium text-white transition-colors {hasValidationErrors
				? 'cursor-not-allowed bg-gray-400'
				: 'bg-blue-500 hover:bg-blue-600'}"
		>
			{t('update')}
		</Button>
	</div>

	<div>
		<p class="text-sm text-gray-600 mb-2">
			{t('work_shift_description_1')}
		</p>
		<p class="text-sm text-gray-600 mb-2">
			{t('work_shift_description_2')}
		</p>
	</div>

	<!-- Validation error summary -->
	{#if hasValidationErrors}
		<Alert color="red" class="mb-4">
			<span class="font-medium">{t('validation_errors') || 'Please fix the following errors:'}</span
			>
			<ul class="mt-2 list-inside list-disc text-sm">
				{#each Object.entries(timeValidationErrors) as [dayIndex, error]}
					<li>{t(getDayTranslationKey(days[dayIndex]?.day))}: {error}</li>
				{/each}
			</ul>
		</Alert>
	{/if}

	<div class="mb-8 flex items-center">
		<span class="mr-2">{t('same_as_business_hours')}</span>
		<Toggle color="blue" bind:checked={sameAsBusinessHours} on:change={toggleSameAsBusinessHours} />
	</div>

	<!-- these hidden fields will POST your JSON -->
	<input type="hidden" name="sameAsBusinessHours" value={sameAsBusinessHours} />

	<input
		type="hidden"
		name="workShiftData"
		value={JSON.stringify(
			sameAsBusinessHours && business_hours?.workShift ? business_hours.workShift : days
		)}
	/>

	<!-- <Alert color="yellow">
        <span class="font-medium">Check!</span>
        {sameAsBusinessHours}
        {JSON.stringify(
            sameAsBusinessHours
            ? business_hours.workShift
            : days
        )}
    </Alert> -->

	{#if sameAsBusinessHours}
		<!-- Read-only display of business hours -->
		<div class="space-y-4">
			{#if business_hours?.workShift && business_hours.workShift.length > 0}
				{#each business_hours.workShift as day, index}
					<div class="mb-4 flex items-center">
						<div class="w-10">
							<!-- Read-only checkbox (visual indicator only) -->
							<input
								type="checkbox"
								id="readonly-day-{index}"
								checked={day.active}
								disabled
								class="h-5 w-5 cursor-not-allowed rounded border-gray-300 bg-gray-100 text-blue-500 opacity-60"
							/>
						</div>
						<label for="readonly-day-{index}" class="w-32 font-medium text-gray-700">
							{t(getDayTranslationKey(day.day))}
						</label>

						{#if day.active && day.times && day.times.length > 0}
							<div class="flex flex-1 items-center">
								<!-- Read-only time display -->
								<div class="w-40 rounded border border-gray-200 bg-gray-50 px-4 py-2 text-gray-700">
									{day.times[0].start}
								</div>

								<span class="mx-3 text-gray-600">{t('to')}</span>

								<div class="w-40 rounded border border-gray-200 bg-gray-50 px-4 py-2 text-gray-700">
									{day.times[0].end}
								</div>
							</div>
						{:else if day.active}
							<div class="flex flex-1 items-center">
								<span class="italic text-gray-500">No times set</span>
							</div>
						{/if}
					</div>
				{/each}
			{:else}
				<p class="text-gray-500">Loading business hours...</p>
			{/if}
		</div>
	{:else}
		<!-- Editable form for custom work schedule -->
		<div>
			{#each days as day, i}
				<div class="mb-4">
					<!-- Day checkbox and label - always on top -->
					<div class="mb-2 flex items-center">
						<div class="w-10">
							<input
								type="checkbox"
								id="day-{i}"
								checked={day.active}
								on:change={() => toggleDay(i)}
								class="h-5 w-5 rounded border-gray-300 bg-gray-100 text-blue-500 focus:ring-2 focus:ring-blue-500"
							/>
						</div>
						<label for="day-{i}" class="w-32 font-medium text-gray-700">
							{t(getDayTranslationKey(day.day))}
						</label>
					</div>

					{#if day.active}
						<!-- Time selection - responsive layout -->
						<div class="ml-10 space-y-2 sm:flex sm:items-center sm:space-x-3 sm:space-y-0 md:flex md:items-center md:space-x-3 md:space-y-0">
							<div class="relative inline-block w-full sm:w-40">
								<select
									bind:value={day.times[0].start}
									on:change={() => handleTimeChange(i, 'start')}
									class="block w-full appearance-none rounded border px-4 py-2 pr-8 leading-tight text-gray-700 focus:outline-none {timeValidationErrors[
										i
									]
										? 'border-red-500 focus:border-red-500'
										: 'border-gray-300 focus:border-blue-500'} bg-white"
								>
									{#each timeOptions as option}
										<option value={option}>{option}</option>
									{/each}
								</select>
							</div>

							<span class="text-center text-gray-600 sm:mx-3 md:mx-3">{t('to')}</span>

							<div class="relative inline-block w-full sm:w-40">
								<select
									bind:value={day.times[0].end}
									on:change={() => handleTimeChange(i, 'end')}
									class="block w-full appearance-none rounded border px-4 py-2 pr-8 leading-tight text-gray-700 focus:outline-none {timeValidationErrors[
										i
									]
										? 'border-red-500 focus:border-red-500'
										: 'border-gray-300 focus:border-blue-500'} bg-white"
								>
									{#each timeOptions as option}
										<option value={option}>{option}</option>
									{/each}
								</select>
							</div>
						</div>

						<!-- Time validation error for this specific day -->
						{#if timeValidationErrors[i]}
							<div class="ml-10 mt-2">
								<Alert color="red" class="px-3 py-2 text-sm">
									{timeValidationErrors[i]}
								</Alert>
							</div>
						{/if}
					{/if}
				</div>
			{/each}
		</div>
	{/if}
</form>
